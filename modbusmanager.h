#ifndef MODBUSMANAGER_H
#define MODBUSMANAGER_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QQueue>
#include <QTimer>
#include <QSerialPort>
#include <QTcpSocket>
#include <QByteArray>

/**
 * @brief Modbus通信管理器
 *
 * 功能特性：
 * - 支持RTU/TCP双模式
 * - 多线程异步通信
 * - 线圈和保持寄存器读写
 * - 连接状态管理
 * - 请求队列管理
 * - 超时处理
 */
class ModbusManager : public QObject
{
    Q_OBJECT

public:
    // 传输模式枚举
    enum TransportMode {
        RTU_MODE = 0,   // RTU串口模式
        TCP_MODE = 1    // TCP网络模式
    };

    // 连接状态枚举
    enum ConnectionState {
        DISCONNECTED = 0,   // 未连接
        CONNECTING = 1,     // 连接中
        CONNECTED = 2,      // 已连接
        ERROR_STATE = 3     // 错误状态
    };

    // Modbus功能码常量
    static const quint8 READ_COILS = 0x01;              // 读线圈
    static const quint8 READ_HOLDING_REGISTERS = 0x03;  // 读保持寄存器
    static const quint8 WRITE_SINGLE_COIL = 0x05;       // 写单个线圈
    static const quint8 WRITE_SINGLE_REGISTER = 0x06;   // 写单个寄存器

    explicit ModbusManager(QObject *parent = nullptr);
    ~ModbusManager();

    // === 线程管理接口 ===
    void startWorkerThread();
    void stopWorkerThread();
    bool isWorkerThreadRunning() const;

    // === 连接管理接口 ===
    int connectRTU(const QString &portName, int baudRate = 9600);
    int connectTCP(const QString &host, quint16 port = 502);
    int disconnectDevice();

    // === 状态查询接口 ===
    TransportMode getTransportMode() const;
    ConnectionState getConnectionState() const;
    bool isConnected() const;

    // === 通信接口 ===
    int readCoils(quint8 slaveId, quint16 startAddr, quint16 quantity);
    int readHoldingRegisters(quint8 slaveId, quint16 startAddr, quint16 quantity);
    int writeSingleCoil(quint8 slaveId, quint16 addr, bool value);
    int writeSingleRegister(quint8 slaveId, quint16 addr, quint16 value);

    // === 配置接口 ===
    void setTimeout(int timeoutMs);
    int getTimeout() const;

signals:
    // 数据接收信号
    void dataReceived(int requestId, quint8 functionCode, const QByteArray &data);

    // 操作完成信号
    void operationCompleted(int requestId, bool success);

    // 连接结果信号
    void connectionResult(int requestId, bool success, const QString &message);

    // 错误信号
    void errorOccurred(int requestId, const QString &error);

    // 连接状态变化信号
    void connectionStateChanged(ConnectionState state);

private:
    // 内部请求结构体
    struct ModbusRequest {
        int requestId;          // 请求ID
        quint8 slaveId;         // 从站地址
        quint8 functionCode;    // 功能码
        quint16 startAddr;      // 起始地址
        quint16 quantity;       // 数量或值
        bool boolValue;         // 布尔值（用于线圈）

        ModbusRequest() : requestId(-1), slaveId(0), functionCode(0),
            startAddr(0), quantity(0), boolValue(false) {}
    };

    // === 线程相关成员 ===
    QThread *m_workerThread;            // 工作线程
    mutable QMutex m_mutex;             // 互斥锁
    QQueue<ModbusRequest> m_requestQueue; // 请求队列
    QWaitCondition m_requestCondition;   // 条件变量
    bool m_stopRequested;               // 停止标志

    // === 通信相关成员 ===
    TransportMode m_transportMode;      // 传输模式
    ConnectionState m_connectionState;  // 连接状态
    QSerialPort *m_serialPort;         // 串口对象
    QTcpSocket *m_tcpSocket;           // TCP套接字
    QTimer *m_timeoutTimer;            // 超时定时器
    QByteArray m_receiveBuffer;        // 接收缓冲区

    // === 请求管理成员 ===
    quint16 m_transactionId;           // TCP事务ID
    int m_currentRequestId;            // 当前请求ID
    int m_nextRequestId;               // 下一个请求ID
    int m_timeoutMs;                   // 超时时间(毫秒)

    // === 连接参数成员 ===
    QString m_rtuPortName;             // RTU端口名
    int m_rtuBaudRate;                 // RTU波特率
    QString m_tcpHost;                 // TCP主机地址
    quint16 m_tcpPort;                 // TCP端口号

private slots:
    void doWork();                     // 工作线程主循环
    void onTimeout();                  // 超时处理

    // === 连接事件处理槽 ===
    void onSerialDataReceived();       // 串口数据接收
    void onSerialError(QSerialPort::SerialPortError error);  // 串口错误
    void onTcpConnected();             // TCP连接成功
    void onTcpDisconnected();          // TCP断开连接
    void onTcpDataReceived();          // TCP数据接收
    void onTcpError(QAbstractSocket::SocketError error);     // TCP错误


private:
    // === 请求管理方法 ===
    int generateRequestId();
    void addRequest(const ModbusRequest &request);
    void processRequest(const ModbusRequest &request);

    // === 请求处理方法 ===
    void processConnectRequest(const ModbusRequest &request);
    void processDisconnectRequest(const ModbusRequest &request);
    void processModbusRequest(const ModbusRequest &request);

    // === 连接管理方法 ===
    bool doConnectRTU();
    bool doConnectTCP();
    void doDisconnect();
    void setConnectionState(ConnectionState state);
    bool isConnectedInternal() const;

    // === 数据处理方法 ===
    void processReceivedData();

    // === 协议处理方法 ===
    QByteArray buildRTURequest(quint8 slaveId, quint8 functionCode, const QByteArray &data);
    QByteArray buildTCPRequest(quint8 slaveId, quint8 functionCode, const QByteArray &data);
    QByteArray buildRequestData(const ModbusRequest &request);
    quint16 calculateCRC(const QByteArray &data);

    // === 通信方法 ===
    bool sendRequest(const QByteArray &request);
    bool waitForResponse(int requestId, quint8 functionCode);
    QByteArray readAvailableData();
    void handleReceivedData(int requestId, quint8 functionCode, const QByteArray &data);

    // === 验证方法 ===
    bool validateRTUResponse(const QByteArray &response);
    bool validateTCPResponse(const QByteArray &response);

    // === 初始化和清理方法 ===
    void initSerialPort();
    void initTcpSocket();
    void cleanupConnection();

    // === 超时管理方法 ===
    void startTimeoutTimer(int requestId);
    void stopTimeoutTimer();

    // === 线程健康检查方法 ===
    bool isWorkerThreadHealthy() const;
    int getQueueSize() const;
    void clearRequestQueue();

    // === 工作线程清理方法 ===
    void cleanupWorkerThread();
};

#endif // MODBUSMANAGER_H
