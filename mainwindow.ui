<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Modbus通信管理器 - 支持RTU/TCP多线程</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="mainLayout">
    <item>
     <layout class="QHBoxLayout" name="topLayout">
      <item>
       <widget class="QGroupBox" name="connectionGroup">
        <property name="title">
         <string>连接设置</string>
        </property>
        <layout class="QGridLayout" name="connectionLayout">
         <item row="0" column="0">
          <widget class="QLabel" name="transportModeLabel">
           <property name="text">
            <string>传输模式:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="transportModeCombo">
           <item>
            <property name="text">
             <string>RTU</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>TCP</string>
            </property>
           </item>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="portLabel">
           <property name="text">
            <string>端口/IP:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="portEdit">
           <property name="placeholderText">
            <string>COM1 或 *************</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="baudRateLabel">
           <property name="text">
            <string>波特率/端口:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QSpinBox" name="baudRateSpinBox">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>115200</number>
           </property>
           <property name="value">
            <number>9600</number>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QPushButton" name="connectButton">
           <property name="text">
            <string>连接</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QPushButton" name="disconnectButton">
           <property name="text">
            <string>断开</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="statusTextLabel">
           <property name="text">
            <string>状态:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLabel" name="statusLabel">
           <property name="text">
            <string>未连接</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="parametersGroup">
        <property name="title">
         <string>通信参数</string>
        </property>
        <layout class="QGridLayout" name="parametersLayout">
         <item row="0" column="0">
          <widget class="QLabel" name="slaveIdLabel">
           <property name="text">
            <string>从站ID:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QSpinBox" name="slaveIdSpinBox">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>247</number>
           </property>
           <property name="value">
            <number>1</number>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="startAddrLabel">
           <property name="text">
            <string>起始地址:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QSpinBox" name="startAddrSpinBox">
           <property name="maximum">
            <number>65535</number>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="quantityLabel">
           <property name="text">
            <string>数量:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QSpinBox" name="quantitySpinBox">
           <property name="minimum">
            <number>1</number>
           </property>
           <property name="maximum">
            <number>125</number>
           </property>
           <property name="value">
            <number>1</number>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="valueLabel">
           <property name="text">
            <string>写入值:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QSpinBox" name="valueSpinBox">
           <property name="maximum">
            <number>65535</number>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="middleLayout">
      <item>
       <widget class="QGroupBox" name="operationGroup">
        <property name="title">
         <string>Modbus操作</string>
        </property>
        <layout class="QGridLayout" name="operationLayout">
         <item row="0" column="0">
          <widget class="QPushButton" name="readCoilsButton">
           <property name="text">
            <string>读取线圈</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QPushButton" name="readRegistersButton">
           <property name="text">
            <string>读取寄存器</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QPushButton" name="writeCoilButton">
           <property name="text">
            <string>写单个线圈</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QPushButton" name="writeRegisterButton">
           <property name="text">
            <string>写单个寄存器</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="autoReadGroup">
        <property name="title">
         <string>自动读取</string>
        </property>
        <layout class="QGridLayout" name="autoReadLayout">
         <item row="0" column="0">
          <widget class="QLabel" name="intervalLabel">
           <property name="text">
            <string>间隔(ms):</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QSpinBox" name="intervalSpinBox">
           <property name="minimum">
            <number>100</number>
           </property>
           <property name="maximum">
            <number>10000</number>
           </property>
           <property name="value">
            <number>1000</number>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QPushButton" name="startAutoReadButton">
           <property name="text">
            <string>开始自动读取</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QPushButton" name="stopAutoReadButton">
           <property name="text">
            <string>停止自动读取</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QGroupBox" name="logGroup">
      <property name="title">
       <string>通信日志</string>
      </property>
      <layout class="QVBoxLayout" name="logLayout">
       <item>
        <widget class="QTextEdit" name="logTextEdit">
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="font">
          <font>
           <family>Consolas</family>
           <pointsize>9</pointsize>
          </font>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="clearLogButton">
         <property name="text">
          <string>清空日志</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1000</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
