#include "modbusmanager.h"
#include <QDebug>
#include <QCoreApplication>

/**
 * @brief 构造函数
 * @param parent 父对象指针
 */
ModbusManager::ModbusManager(QObject *parent)
    : QObject(parent)
    , m_workerThread(nullptr)
    , m_stopRequested(false)
    , m_transportMode(RTU_MODE)
    , m_connectionState(DISCONNECTED)
    , m_serialPort(nullptr)
    , m_tcpSocket(nullptr)
    , m_timeoutTimer(nullptr)
    , m_transactionId(0)
    , m_currentRequestId(-1)
    , m_nextRequestId(1)
    , m_timeoutMs(3000)
    , m_rtuBaudRate(9600)
    , m_tcpPort(502)
{
    qDebug() << "ModbusManager created in thread:" << QThread::currentThread();
}

/**
 * @brief 析构函数
 */
ModbusManager::~ModbusManager()
{
    qDebug() << "ModbusManager destructor called";
    stopWorkerThread();
}

/**
 * @brief RTU连接接口（主线程调用）
 * @param portName 串口名称
 * @param baudRate 波特率
 * @return 请求ID
 */
int ModbusManager::connectRTU(const QString &portName, int baudRate)
{
    if (!isWorkerThreadHealthy()) {
        qWarning() << "Worker thread is not running";
        return -1;
    }

    int requestId = generateRequestId();

    ModbusRequest request;
    request.requestId = requestId;
    request.functionCode = 0xFF; // 特殊功能码表示连接请求
    request.slaveId = 0; // RTU模式标识
    request.startAddr = static_cast<quint16>(baudRate);
    request.quantity = 0;

    // 保存连接参数
    m_rtuPortName = portName;
    m_rtuBaudRate = baudRate;
    m_transportMode = RTU_MODE;

    addRequest(request);

    qDebug() << "RTU connect request queued - Port:" << portName << "BaudRate:" << baudRate;
    return requestId;
}

/**
 * @brief TCP连接接口（主线程调用）
 * @param host 主机地址
 * @param port 端口号
 * @return 请求ID
 */
int ModbusManager::connectTCP(const QString &host, quint16 port)
{
    if (!isWorkerThreadHealthy()) {
        qWarning() << "Worker thread is not running";
        return -1;
    }

    int requestId = generateRequestId();

    ModbusRequest request;
    request.requestId = requestId;
    request.functionCode = 0xFF; // 特殊功能码表示连接请求
    request.slaveId = 1; // TCP模式标识
    request.startAddr = 0;
    request.quantity = port;

    // 保存连接参数
    m_tcpHost = host;
    m_tcpPort = port;
    m_transportMode = TCP_MODE;

    addRequest(request);

    qDebug() << "TCP connect request queued - Host:" << host << "Port:" << port;
    return requestId;
}

/**
 * @brief 断开连接接口（主线程调用）
 * @return 请求ID
 */
int ModbusManager::disconnectDevice()
{
    if (!isWorkerThreadHealthy()) {
        qWarning() << "Worker thread is not running";
        return -1;
    }

    int requestId = generateRequestId();

    ModbusRequest request;
    request.requestId = requestId;
    request.functionCode = 0xFE; // 特殊功能码表示断开连接请求

    addRequest(request);

    qDebug() << "Disconnect request queued";
    return requestId;
}

/**
 * @brief 读取线圈接口（主线程调用）
 * @param slaveId 从站地址
 * @param startAddr 起始地址
 * @param quantity 数量
 * @return 请求ID
 */
int ModbusManager::readCoils(quint8 slaveId, quint16 startAddr, quint16 quantity)
{
    if (!isWorkerThreadHealthy()) {
        qWarning() << "Worker thread is not running";
        return -1;
    }

    if (!isConnected()) {
        qWarning() << "Device is not connected";
        return -1;
    }

    int requestId = generateRequestId();

    ModbusRequest request;
    request.requestId = requestId;
    request.slaveId = slaveId;
    request.functionCode = READ_COILS;
    request.startAddr = startAddr;
    request.quantity = quantity;

    addRequest(request);

    qDebug() << "Read coils request queued - Slave:" << slaveId
             << "Addr:" << startAddr << "Qty:" << quantity;
    return requestId;
}

/**
 * @brief 读取保持寄存器接口（主线程调用）
 * @param slaveId 从站地址
 * @param startAddr 起始地址
 * @param quantity 数量
 * @return 请求ID
 */
int ModbusManager::readHoldingRegisters(quint8 slaveId, quint16 startAddr, quint16 quantity)
{
    if (!isWorkerThreadHealthy()) {
        qWarning() << "Worker thread is not running";
        return -1;
    }

    if (!isConnected()) {
        qWarning() << "Device is not connected";
        return -1;
    }

    int requestId = generateRequestId();

    ModbusRequest request;
    request.requestId = requestId;
    request.slaveId = slaveId;
    request.functionCode = READ_HOLDING_REGISTERS;
    request.startAddr = startAddr;
    request.quantity = quantity;

    addRequest(request);

    qDebug() << "Read holding registers request queued - Slave:" << slaveId
             << "Addr:" << startAddr << "Qty:" << quantity;
    return requestId;
}

/**
 * @brief 写单个线圈接口（主线程调用）
 * @param slaveId 从站地址
 * @param addr 地址
 * @param value 值
 * @return 请求ID
 */
int ModbusManager::writeSingleCoil(quint8 slaveId, quint16 addr, bool value)
{
    if (!isWorkerThreadHealthy()) {
        qWarning() << "Worker thread is not running";
        return -1;
    }

    if (!isConnected()) {
        qWarning() << "Device is not connected";
        return -1;
    }

    int requestId = generateRequestId();

    ModbusRequest request;
    request.requestId = requestId;
    request.slaveId = slaveId;
    request.functionCode = WRITE_SINGLE_COIL;
    request.startAddr = addr;
    request.boolValue = value;

    addRequest(request);

    qDebug() << "Write single coil request queued - Slave:" << slaveId
             << "Addr:" << addr << "Value:" << value;
    return requestId;
}

/**
 * @brief 写单个寄存器接口（主线程调用）
 * @param slaveId 从站地址
 * @param addr 地址
 * @param value 值
 * @return 请求ID
 */
int ModbusManager::writeSingleRegister(quint8 slaveId, quint16 addr, quint16 value)
{
    if (!isWorkerThreadHealthy()) {
        qWarning() << "Worker thread is not running";
        return -1;
    }

    if (!isConnected()) {
        qWarning() << "Device is not connected";
        return -1;
    }

    int requestId = generateRequestId();

    ModbusRequest request;
    request.requestId = requestId;
    request.slaveId = slaveId;
    request.functionCode = WRITE_SINGLE_REGISTER;
    request.startAddr = addr;
    request.quantity = value;

    addRequest(request);

    qDebug() << "Write single register request queued - Slave:" << slaveId
             << "Addr:" << addr << "Value:" << value;
    return requestId;
}

/**
 * @brief 获取传输模式
 * @return 当前传输模式
 */
ModbusManager::TransportMode ModbusManager::getTransportMode() const
{
    return m_transportMode;
}

/**
 * @brief 获取连接状态
 * @return 当前连接状态
 */
ModbusManager::ConnectionState ModbusManager::getConnectionState() const
{
    return m_connectionState;
}

/**
 * @brief 检查是否已连接
 * @return true表示已连接
 */
bool ModbusManager::isConnected() const
{
    return m_connectionState == CONNECTED;
}

/**
 * @brief 启动工作线程
 */
void ModbusManager::startWorkerThread()
{
    if (m_workerThread && m_workerThread->isRunning()) {
        qDebug() << "Worker thread is already running";
        return;
    }

    m_stopRequested = false;
    m_workerThread = new QThread();

    // 将当前对象移动到工作线程
    this->moveToThread(m_workerThread);

    // 连接线程信号
    connect(m_workerThread, &QThread::started, this, &ModbusManager::doWork);
    connect(m_workerThread, &QThread::finished, m_workerThread, &QThread::deleteLater);

    // 启动线程
    m_workerThread->start();

    qDebug() << "ModbusManager worker thread started";
}

/**
 * @brief 停止工作线程
 */
void ModbusManager::stopWorkerThread()
{
    if (!m_workerThread || !m_workerThread->isRunning()) {
        return;
    }

    // 设置停止标志并唤醒工作线程
    {
        QMutexLocker locker(&m_mutex);
        m_stopRequested = true;
        m_requestCondition.wakeAll();
    }

    // 等待线程结束
    if (m_workerThread->wait(5000)) {
        qDebug() << "Worker thread stopped successfully";
    } else {
        qDebug() << "Worker thread stop timeout, terminating...";
        m_workerThread->terminate();
        m_workerThread->wait(1000);
    }

    m_workerThread = nullptr;
}

/**
 * @brief 检查工作线程是否正在运行
 * @return true表示正在运行
 */
bool ModbusManager::isWorkerThreadRunning() const
{
    return m_workerThread && m_workerThread->isRunning();
}

/**
 * @brief 生成新的请求ID
 * @return 新的请求ID
 */
int ModbusManager::generateRequestId()
{
    return m_nextRequestId++;
}

/**
 * @brief 添加请求到队列
 * @param request 请求对象
 */
void ModbusManager::addRequest(const ModbusRequest &request)
{
    QMutexLocker locker(&m_mutex);
    m_requestQueue.enqueue(request);
    m_requestCondition.wakeOne();
}

/**
 * @brief 设置连接状态
 * @param state 新的连接状态
 */
void ModbusManager::setConnectionState(ConnectionState state)
{
    if (m_connectionState != state) {
        m_connectionState = state;
        emit connectionStateChanged(state);
    }
}

/**
 * @brief 工作线程主循环
 *
 * 这是工作线程的核心方法，负责：
 * 1. 处理请求队列中的任务
 * 2. 管理连接状态
 * 3. 处理超时和错误
 * 4. 响应停止信号
 */
void ModbusManager::doWork()
{
    qDebug() << "ModbusManager worker thread started, thread ID:" << QThread::currentThread();

    // 在工作线程中初始化定时器
    m_timeoutTimer = new QTimer();
    m_timeoutTimer->setSingleShot(true);
    connect(m_timeoutTimer, &QTimer::timeout, this, &ModbusManager::onTimeout);

    // 主工作循环
    while (!m_stopRequested) {
        ModbusRequest request;
        bool hasRequest = false;

        // 等待新请求或停止信号
        {
            QMutexLocker locker(&m_mutex);

            // 如果队列为空且未请求停止，则等待
            while (m_requestQueue.isEmpty() && !m_stopRequested) {
                qDebug() << "Worker thread waiting for requests...";
                m_requestCondition.wait(&m_mutex);
            }

            // 检查是否需要停止
            if (m_stopRequested) {
                qDebug() << "Worker thread received stop request";
                break;
            }

            // 获取请求
            if (!m_requestQueue.isEmpty()) {
                request = m_requestQueue.dequeue();
                hasRequest = true;
                qDebug() << "Worker thread processing request ID:" << request.requestId;
            }
        }

        // 处理请求
        if (hasRequest) {
            processRequest(request);
        }
    }

    // 清理工作线程资源
    cleanupWorkerThread();

    qDebug() << "ModbusManager worker thread finished";
}

/**
 * @brief 处理单个请求
 * @param request 要处理的请求
 */
void ModbusManager::processRequest(const ModbusRequest &request)
{
    qDebug() << "Processing request - ID:" << request.requestId
             << "Type:" << request.functionCode
             << "SlaveId:" << request.slaveId;

    // 设置当前请求ID用于超时处理
    m_currentRequestId = request.requestId;

    try {
        // 根据功能码分发请求
        switch (request.functionCode) {
            case 0xFF: // 特殊功能码：连接请求
                processConnectRequest(request);
                break;

            case 0xFE: // 特殊功能码：断开连接请求
                processDisconnectRequest(request);
                break;

            case READ_COILS:
            case READ_HOLDING_REGISTERS:
            case WRITE_SINGLE_COIL:
            case WRITE_SINGLE_REGISTER:
                processModbusRequest(request);
                break;

            default:
                qWarning() << "Unknown function code:" << request.functionCode;
                emit errorOccurred(request.requestId, "未知的功能码");
                break;
        }
    }
    catch (const std::exception &e) {
        qCritical() << "Exception in processRequest:" << e.what();
        emit errorOccurred(request.requestId, QString("处理请求时发生异常: %1").arg(e.what()));
    }
    catch (...) {
        qCritical() << "Unknown exception in processRequest";
        emit errorOccurred(request.requestId, "处理请求时发生未知异常");
    }

    // 重置当前请求ID
    m_currentRequestId = -1;
}

/**
 * @brief 超时处理槽函数
 */
void ModbusManager::onTimeout()
{
    qWarning() << "Request timeout - ID:" << m_currentRequestId;

    // 停止当前操作
    if (m_currentRequestId != -1) {
        emit errorOccurred(m_currentRequestId, "请求超时");
        m_currentRequestId = -1;
    }

    // 如果是连接操作超时，设置为错误状态
    if (m_connectionState == CONNECTING) {
        setConnectionState(ERROR_STATE);
    }
}

/**
 * @brief 清理工作线程资源
 */
void ModbusManager::cleanupWorkerThread()
{
    qDebug() << "Cleaning up worker thread resources";

    // 停止超时定时器
    if (m_timeoutTimer) {
        m_timeoutTimer->stop();
        delete m_timeoutTimer;
        m_timeoutTimer = nullptr;
    }

    // 断开连接
    doDisconnect();

    // 清空请求队列
    {
        QMutexLocker locker(&m_mutex);
        while (!m_requestQueue.isEmpty()) {
            ModbusRequest request = m_requestQueue.dequeue();
            emit errorOccurred(request.requestId, "工作线程已停止");
        }
    }

    qDebug() << "Worker thread cleanup completed";
}

/**
 * @brief 启动超时定时器
 * @param requestId 请求ID
 */
void ModbusManager::startTimeoutTimer(int requestId)
{
    if (m_timeoutTimer && m_timeoutMs > 0) {
        m_currentRequestId = requestId;
        m_timeoutTimer->start(m_timeoutMs);
        qDebug() << "Timeout timer started for request" << requestId << "timeout:" << m_timeoutMs << "ms";
    }
}

/**
 * @brief 停止超时定时器
 */
void ModbusManager::stopTimeoutTimer()
{
    if (m_timeoutTimer && m_timeoutTimer->isActive()) {
        m_timeoutTimer->stop();
        qDebug() << "Timeout timer stopped for request" << m_currentRequestId;
    }
}

/**
 * @brief 检查工作线程状态
 * @return true表示工作线程健康运行
 */
bool ModbusManager::isWorkerThreadHealthy() const
{
    return m_workerThread &&
           m_workerThread->isRunning() &&
           !m_stopRequested;
}

/**
 * @brief 获取请求队列大小
 * @return 队列中待处理的请求数量
 */
int ModbusManager::getQueueSize() const
{
    QMutexLocker locker(&m_mutex);
    return m_requestQueue.size();
}

/**
 * @brief 清空请求队列
 */
void ModbusManager::clearRequestQueue()
{
    QMutexLocker locker(&m_mutex);

    while (!m_requestQueue.isEmpty()) {
        ModbusRequest request = m_requestQueue.dequeue();
        emit errorOccurred(request.requestId, "请求队列已清空");
    }

    qDebug() << "Request queue cleared";
}

/**
 * @brief 设置超时时间
 * @param timeoutMs 超时时间（毫秒）
 */
void ModbusManager::setTimeout(int timeoutMs)
{
    if (timeoutMs > 0) {
        m_timeoutMs = timeoutMs;
        qDebug() << "Timeout set to:" << timeoutMs << "ms";
    } else {
        qWarning() << "Invalid timeout value:" << timeoutMs;
    }
}

/**
 * @brief 获取超时时间
 * @return 超时时间（毫秒）
 */
int ModbusManager::getTimeout() const
{
    return m_timeoutMs;
}

/**
 * @brief 处理连接请求 (工作线程中调用)
 * @param request 连接请求
 */
void ModbusManager::processConnectRequest(const ModbusRequest &request)
{
    qDebug() << "Processing connect request - Mode:" << request.slaveId;

    // 设置连接状态为连接中
    setConnectionState(CONNECTING);

    // 启动超时定时器
    startTimeoutTimer(request.requestId);

    bool success = false;
    QString message;

    try {
        if (request.slaveId == 0) {
            // RTU模式连接
            success = doConnectRTU();
            message = success ? "RTU连接成功" : "RTU连接失败";
        } else {
            // TCP模式连接
            success = doConnectTCP();
            message = success ? "TCP连接成功" : "TCP连接失败";
        }
    }
    catch (const std::exception &e) {
        success = false;
        message = QString("连接异常: %1").arg(e.what());
    }

    // 停止超时定时器
    stopTimeoutTimer();

    // 设置最终连接状态
    setConnectionState(success ? CONNECTED : ERROR_STATE);

    // 发送连接结果信号
    emit connectionResult(request.requestId, success, message);
}

/**
 * @brief 处理断开连接请求 (工作线程中调用)
 * @param request 断开连接请求
 */
void ModbusManager::processDisconnectRequest(const ModbusRequest &request)
{
    qDebug() << "Processing disconnect request";

    // 启动超时定时器
    startTimeoutTimer(request.requestId);

    // 执行断开连接
    doDisconnect();

    // 停止超时定时器
    stopTimeoutTimer();

    // 发送操作完成信号
    emit operationCompleted(request.requestId, true);
}

/**
 * @brief 处理Modbus通信请求 (工作线程中调用)
 * @param request Modbus请求
 */
void ModbusManager::processModbusRequest(const ModbusRequest &request)
{
    qDebug() << "Processing Modbus request - Function:" << request.functionCode;

    // 检查连接状态
    if (!isConnectedInternal()) {
        emit errorOccurred(request.requestId, "设备未连接");
        return;
    }

    // 启动超时定时器
    startTimeoutTimer(request.requestId);

    try {
        // 构建请求数据
        QByteArray requestData = buildRequestData(request);

        // 根据传输模式构建完整请求
        QByteArray fullRequest;
        if (m_transportMode == RTU_MODE) {
            fullRequest = buildRTURequest(request.slaveId, request.functionCode, requestData);
        } else {
            fullRequest = buildTCPRequest(request.slaveId, request.functionCode, requestData);
        }

        // 发送请求
        if (sendRequest(fullRequest)) {
            // 等待响应
            if (!waitForResponse(request.requestId, request.functionCode)) {
                emit errorOccurred(request.requestId, "等待响应超时");
            }
        } else {
            emit errorOccurred(request.requestId, "发送请求失败");
        }
    }
    catch (const std::exception &e) {
        emit errorOccurred(request.requestId, QString("处理请求异常: %1").arg(e.what()));
    }

    // 停止超时定时器
    stopTimeoutTimer();
}

/**
 * @brief RTU模式连接实现
 * @return true表示连接成功
 */
bool ModbusManager::doConnectRTU()
{
    qDebug() << "Attempting RTU connection - Port:" << m_rtuPortName << "BaudRate:" << m_rtuBaudRate;

    // 清理现有连接
    doDisconnect();

    // 创建串口对象
    m_serialPort = new QSerialPort();

    // 配置串口参数
    m_serialPort->setPortName(m_rtuPortName);
    m_serialPort->setBaudRate(m_rtuBaudRate);
    m_serialPort->setDataBits(QSerialPort::Data8);
    m_serialPort->setParity(QSerialPort::NoParity);
    m_serialPort->setStopBits(QSerialPort::OneStop);
    m_serialPort->setFlowControl(QSerialPort::NoFlowControl);

    // 尝试打开串口
    if (!m_serialPort->open(QIODevice::ReadWrite)) {
        QString error = m_serialPort->errorString();
        qWarning() << "Failed to open serial port:" << error;

        delete m_serialPort;
        m_serialPort = nullptr;
        return false;
    }

    // 清空缓冲区
    m_serialPort->clear();
    m_receiveBuffer.clear();

    // 连接串口信号
    connect(m_serialPort, &QSerialPort::readyRead,
            this, &ModbusManager::onSerialDataReceived);
    connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::error),
            this, &ModbusManager::onSerialError);

    qDebug() << "RTU connection established successfully";
    return true;
}

/**
 * @brief TCP模式连接实现
 * @return true表示连接成功
 */
bool ModbusManager::doConnectTCP()
{
    qDebug() << "Attempting TCP connection - Host:" << m_tcpHost << "Port:" << m_tcpPort;

    // 清理现有连接
    doDisconnect();

    // 创建TCP套接字
    m_tcpSocket = new QTcpSocket();

    // 连接TCP信号
    connect(m_tcpSocket, &QTcpSocket::connected,
            this, &ModbusManager::onTcpConnected);
    connect(m_tcpSocket, &QTcpSocket::disconnected,
            this, &ModbusManager::onTcpDisconnected);
    connect(m_tcpSocket, &QTcpSocket::readyRead,
            this, &ModbusManager::onTcpDataReceived);
    connect(m_tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
            this, &ModbusManager::onTcpError);

    // 尝试连接
    m_tcpSocket->connectToHost(m_tcpHost, m_tcpPort);

    // 等待连接完成 (阻塞等待，但在工作线程中是安全的)
    if (!m_tcpSocket->waitForConnected(m_timeoutMs)) {
        QString error = m_tcpSocket->errorString();
        qWarning() << "Failed to connect to TCP host:" << error;

        delete m_tcpSocket;
        m_tcpSocket = nullptr;
        return false;
    }

    // 清空缓冲区
    m_receiveBuffer.clear();
    m_transactionId = 0;

    qDebug() << "TCP connection established successfully";
    return true;
}

/**
 * @brief 断开连接实现
 */
void ModbusManager::doDisconnect()
{
    qDebug() << "Disconnecting device";

    // 断开串口连接
    if (m_serialPort) {
        if (m_serialPort->isOpen()) {
            m_serialPort->close();
        }
        delete m_serialPort;
        m_serialPort = nullptr;
    }

    // 断开TCP连接
    if (m_tcpSocket) {
        if (m_tcpSocket->state() == QAbstractSocket::ConnectedState) {
            m_tcpSocket->disconnectFromHost();
            m_tcpSocket->waitForDisconnected(1000);
        }
        delete m_tcpSocket;
        m_tcpSocket = nullptr;
    }

    // 清空缓冲区
    m_receiveBuffer.clear();

    // 设置连接状态
    setConnectionState(DISCONNECTED);

    qDebug() << "Device disconnected";
}

/**
 * @brief 设置连接状态
 * @param state 新的连接状态
 */
void ModbusManager::setConnectionState(ConnectionState state)
{
    if (m_connectionState != state) {
        m_connectionState = state;
        emit connectionStateChanged(state);
    }
}

/**
 * @brief 内部连接状态检查
 * @return true表示已连接
 */
bool ModbusManager::isConnectedInternal() const
{
    if (m_transportMode == RTU_MODE) {
        return m_serialPort && m_serialPort->isOpen();
    } else {
        return m_tcpSocket && m_tcpSocket->state() == QAbstractSocket::ConnectedState;
    }
}

/**
 * @brief 串口数据接收处理
 */
void ModbusManager::onSerialDataReceived()
{
    if (!m_serialPort) return;

    QByteArray data = m_serialPort->readAll();
    m_receiveBuffer.append(data);

    qDebug() << "Serial data received:" << data.toHex();

    // 处理接收到的数据
    processReceivedData();
}

/**
 * @brief 串口错误处理
 * @param error 错误类型
 */
void ModbusManager::onSerialError(QSerialPort::SerialPortError error)
{
    if (error != QSerialPort::NoError) {
        QString errorString = m_serialPort ? m_serialPort->errorString() : "Unknown error";
        qWarning() << "Serial port error:" << errorString;

        setConnectionState(ERROR_STATE);
        emit errorOccurred(m_currentRequestId, QString("串口错误: %1").arg(errorString));
    }
}

/**
 * @brief TCP连接成功处理
 */
void ModbusManager::onTcpConnected()
{
    qDebug() << "TCP connected successfully";
}

/**
 * @brief TCP断开连接处理
 */
void ModbusManager::onTcpDisconnected()
{
    qDebug() << "TCP disconnected";
    setConnectionState(DISCONNECTED);
}

/**
 * @brief TCP数据接收处理
 */
void ModbusManager::onTcpDataReceived()
{
    if (!m_tcpSocket) return;

    QByteArray data = m_tcpSocket->readAll();
    m_receiveBuffer.append(data);

    qDebug() << "TCP data received:" << data.toHex();

    // 处理接收到的数据
    processReceivedData();
}

/**
 * @brief TCP错误处理
 * @param error 错误类型
 */
void ModbusManager::onTcpError(QAbstractSocket::SocketError error)
{
    if (error != QAbstractSocket::RemoteHostClosedError) {
        QString errorString = m_tcpSocket ? m_tcpSocket->errorString() : "Unknown error";
        qWarning() << "TCP socket error:" << errorString;

        setConnectionState(ERROR_STATE);
        emit errorOccurred(m_currentRequestId, QString("TCP错误: %1").arg(errorString));
    }
}


