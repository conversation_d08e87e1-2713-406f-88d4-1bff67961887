#ifndef IMODBUSTRANSPORT_H
#define IMODBUSTRANSPORT_H

#include <QObject>
#include <QByteArray>
#include "ModbusTypes.h"

/**
 * @file IModbusTransport.h
 * @brief Modbus传输层接口定义
 * <AUTHOR> Team
 * @date 2025-07-30
 */

namespace ModbusManager {

/**
 * @brief Modbus传输层抽象接口
 * 
 * 定义了Modbus传输层的标准接口，支持RTU和TCP两种传输模式。
 * 所有具体的传输实现都应该继承此接口。
 */
class IModbusTransport : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit IModbusTransport(QObject *parent = nullptr) : QObject(parent) {}
    
    /**
     * @brief 虚析构函数
     */
    virtual ~IModbusTransport() = default;

    /**
     * @brief 获取传输模式
     * @return 传输模式
     */
    virtual TransportMode getTransportMode() const = 0;

    /**
     * @brief 获取连接状态
     * @return 连接状态
     */
    virtual ConnectionState getConnectionState() const = 0;

    /**
     * @brief 连接到设备
     * @param config 连接配置
     * @return 连接是否成功
     */
    virtual bool connectToDevice(const ConnectionConfig &config) = 0;

    /**
     * @brief 断开连接
     */
    virtual void disconnectFromDevice() = 0;

    /**
     * @brief 发送数据
     * @param data 要发送的数据
     * @return 发送是否成功
     */
    virtual bool sendData(const QByteArray &data) = 0;

    /**
     * @brief 接收数据
     * @param timeout 超时时间(毫秒)
     * @return 接收到的数据
     */
    virtual QByteArray receiveData(int timeout = 3000) = 0;

    /**
     * @brief 检查是否有数据可读
     * @return 是否有数据可读
     */
    virtual bool isDataAvailable() const = 0;

    /**
     * @brief 清空接收缓冲区
     */
    virtual void clearReceiveBuffer() = 0;

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    virtual QString getLastError() const = 0;

    /**
     * @brief 设置响应超时时间
     * @param timeout 超时时间(毫秒)
     */
    virtual void setResponseTimeout(int timeout) = 0;

    /**
     * @brief 获取响应超时时间
     * @return 超时时间(毫秒)
     */
    virtual int getResponseTimeout() const = 0;

signals:
    /**
     * @brief 连接状态改变信号
     * @param state 新的连接状态
     */
    void connectionStateChanged(ConnectionState state);

    /**
     * @brief 数据接收信号
     * @param data 接收到的数据
     */
    void dataReceived(const QByteArray &data);

    /**
     * @brief 错误发生信号
     * @param errorType 错误类型
     * @param errorMessage 错误消息
     */
    void errorOccurred(ErrorType errorType, const QString &errorMessage);

    /**
     * @brief 数据发送信号
     * @param data 发送的数据
     */
    void dataSent(const QByteArray &data);
};

} // namespace ModbusManager

#endif // IMODBUSTRANSPORT_H
